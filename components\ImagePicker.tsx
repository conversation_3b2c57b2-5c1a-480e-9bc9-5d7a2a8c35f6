import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, Platform } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { Camera, Image as ImageIcon, Plus } from 'lucide-react-native';
import { getImageConstraints, validateImage, hasImageSupport } from '@/utils/imageValidation';

export interface SelectedImage {
  uri: string;
  width: number;
  height: number;
  size: number;
  type?: string;
  fileName?: string;
}

interface ImagePickerComponentProps {
  selectedPlatforms: string[];
  onImagesSelected: (images: SelectedImage[]) => void;
  currentImages: SelectedImage[];
  disabled?: boolean;
}

export default function ImagePickerComponent({
  selectedPlatforms,
  onImagesSelected,
  currentImages,
  disabled = false
}: ImagePickerComponentProps) {
  const constraints = getImageConstraints(selectedPlatforms);
  const supportsImages = hasImageSupport(selectedPlatforms);

  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      // Request camera permissions
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      if (cameraPermission.status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Camera permission is required to take photos. Please enable it in your device settings.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Request media library permissions
      const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (mediaPermission.status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Photo library permission is required to select images. Please enable it in your device settings.',
          [{ text: 'OK' }]
        );
        return false;
      }
    }
    return true;
  };

  const processImageResult = async (result: ImagePicker.ImagePickerResult): Promise<SelectedImage[]> => {
    if (result.canceled || !result.assets) {
      return [];
    }

    const processedImages: SelectedImage[] = [];

    for (const asset of result.assets) {
      // Get file size (estimate if not available)
      let fileSize = asset.fileSize || 0;
      
      // If file size is not available, estimate based on dimensions
      if (fileSize === 0 && asset.width && asset.height) {
        // Rough estimate: width * height * 3 bytes per pixel (RGB) * compression factor
        fileSize = asset.width * asset.height * 3 * 0.5; // Assuming 50% compression
      }

      const selectedImage: SelectedImage = {
        uri: asset.uri,
        width: asset.width || 0,
        height: asset.height || 0,
        size: fileSize,
        type: asset.type,
        fileName: asset.fileName || `image_${Date.now()}.jpg`
      };

      // Validate the image
      const validation = validateImage(selectedImage.uri, selectedImage.size, constraints);
      
      if (!validation.isValid) {
        Alert.alert(
          'Invalid Image',
          `${selectedImage.fileName || 'Selected image'} cannot be used:\n\n${validation.errors.join('\n')}`,
          [{ text: 'OK' }]
        );
        continue;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        Alert.alert(
          'Image Warning',
          `${selectedImage.fileName || 'Selected image'}:\n\n${validation.warnings.join('\n')}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Use Anyway', onPress: () => processedImages.push(selectedImage) }
          ]
        );
      } else {
        processedImages.push(selectedImage);
      }
    }

    return processedImages;
  };

  const pickFromGallery = async () => {
    if (!supportsImages) {
      Alert.alert('Not Supported', 'None of the selected platforms support images.');
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [1, 1],
        allowsEditing: false,
      });

      const newImages = await processImageResult(result);
      if (newImages.length > 0) {
        const allImages = [...currentImages, ...newImages];
        
        // Check total count
        if (constraints.maxCount && allImages.length > constraints.maxCount) {
          Alert.alert(
            'Too Many Images',
            `You can only select up to ${constraints.maxCount} images for the selected platforms.`,
            [{ text: 'OK' }]
          );
          return;
        }
        
        onImagesSelected(allImages);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick images from gallery.');
      console.error('Gallery picker error:', error);
    }
  };

  const takePhoto = async () => {
    if (!supportsImages) {
      Alert.alert('Not Supported', 'None of the selected platforms support images.');
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
        aspect: [1, 1],
        allowsEditing: true,
      });

      const newImages = await processImageResult(result);
      if (newImages.length > 0) {
        const allImages = [...currentImages, ...newImages];
        
        // Check total count
        if (constraints.maxCount && allImages.length > constraints.maxCount) {
          Alert.alert(
            'Too Many Images',
            `You can only select up to ${constraints.maxCount} images for the selected platforms.`,
            [{ text: 'OK' }]
          );
          return;
        }
        
        onImagesSelected(allImages);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo.');
      console.error('Camera error:', error);
    }
  };

  const showImageOptions = () => {
    Alert.alert(
      'Add Images',
      'Choose how you want to add images',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Gallery', onPress: pickFromGallery },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  if (!supportsImages) {
    return (
      <TouchableOpacity style={[styles.button, styles.disabledButton]} disabled>
        <ImageIcon size={20} color="#9CA3AF" />
        <Text style={styles.disabledButtonText}>Images not supported</Text>
      </TouchableOpacity>
    );
  }

  const isAtLimit = constraints.maxCount && currentImages.length >= constraints.maxCount;

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          disabled && styles.disabledButton,
          isAtLimit && styles.disabledButton
        ]}
        onPress={showImageOptions}
        disabled={disabled || isAtLimit}
      >
        <Plus size={20} color={disabled || isAtLimit ? "#9CA3AF" : "#6B7280"} />
        <Text style={[
          styles.buttonText,
          (disabled || isAtLimit) && styles.disabledButtonText
        ]}>
          {isAtLimit ? 'Image limit reached' : 'Add Images'}
        </Text>
      </TouchableOpacity>
      
      {selectedPlatforms.length > 0 && (
        <Text style={styles.constraintsText}>
          Max: {constraints.maxSize}MB, Formats: {constraints.supportedFormats.join(', ')}
          {constraints.maxCount && ` (${currentImages.length}/${constraints.maxCount})`}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  disabledButton: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
  },
  buttonText: {
    fontSize: 16,
    color: '#6B7280',
    marginLeft: 8,
    fontWeight: '500',
  },
  disabledButtonText: {
    color: '#9CA3AF',
  },
  constraintsText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 8,
  },
});
