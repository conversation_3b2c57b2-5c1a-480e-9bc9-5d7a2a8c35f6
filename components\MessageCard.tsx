import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ScrollView } from 'react-native';
import { Clock, CircleCheck as CheckCircle, CircleAlert as AlertCircle, CreditCard as Edit3, Image as ImageIcon, Edit } from 'lucide-react-native';
import { Message } from '@/types';
import { PLATFORMS } from '@/constants/platforms';

interface MessageCardProps {
  message: Message;
  onPress: () => void;
}

export default function MessageCard({ message, onPress }: MessageCardProps) {
  const getStatusIcon = () => {
    switch (message.status) {
      case 'sent':
        return <CheckCircle size={20} color="#10B981" />;
      case 'scheduled':
        return <Clock size={20} color="#F59E0B" />;
      case 'failed':
        return <AlertCircle size={20} color="#EF4444" />;
      default:
        return <Edit3 size={20} color="#6B7280" />;
    }
  };

  const getStatusColor = () => {
    switch (message.status) {
      case 'sent':
        return '#10B981';
      case 'scheduled':
        return '#F59E0B';
      case 'failed':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getDisplayDate = () => {
    if (message.sentDate) return formatDate(message.sentDate);
    if (message.scheduledDate) return formatDate(message.scheduledDate);
    return formatDate(message.createdAt);
  };

  const getPlatformColors = () => {
    return message.platforms.map(platformId => 
      PLATFORMS.find(p => p.id === platformId)?.color || '#6B7280'
    );
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          {getStatusIcon()}
          <Text style={[styles.status, { color: getStatusColor() }]}>
            {message.status.charAt(0).toUpperCase() + message.status.slice(1)}
          </Text>
        </View>
        <Text style={styles.date}>{getDisplayDate()}</Text>
      </View>

      <Text style={styles.content} numberOfLines={3}>
        {message.content}
      </Text>

      {message.images && message.images.length > 0 && (
        <View style={styles.imagesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.imagesScroll}
          >
            {message.images.map((imageUri, index) => (
              <Image
                key={index}
                source={{ uri: imageUri }}
                style={styles.messageImage}
              />
            ))}
          </ScrollView>
          <View style={styles.imageCount}>
            <ImageIcon size={12} color="#6B7280" />
            <Text style={styles.imageCountText}>
              {message.images.length} image{message.images.length !== 1 ? 's' : ''}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.footer}>
        <View style={styles.platforms}>
          {getPlatformColors().map((color, index) => (
            <View key={index} style={[styles.platformDot, { backgroundColor: color }]} />
          ))}
          <Text style={styles.platformCount}>
            {message.platforms.length} platform{message.platforms.length !== 1 ? 's' : ''}
          </Text>
        </View>
        {message.status === 'draft' && (
          <View style={styles.draftIndicator}>
            <Edit size={14} color="#3B82F6" />
            <Text style={styles.draftText}>Tap to edit</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  status: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  date: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  content: {
    fontSize: 16,
    color: '#111827',
    lineHeight: 24,
    marginBottom: 12,
  },
  imagesContainer: {
    marginBottom: 12,
  },
  imagesScroll: {
    marginBottom: 8,
  },
  messageImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#F3F4F6',
  },
  imageCount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageCountText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  platforms: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  platformDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  platformCount: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  draftIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  draftText: {
    fontSize: 11,
    color: '#3B82F6',
    marginLeft: 4,
    fontWeight: '500',
  },
});