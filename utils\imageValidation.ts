import { PLATFORMS } from '@/constants/platforms';
import { SocialPlatform } from '@/types';

export interface ImageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ImageConstraints {
  maxSize: number; // in MB
  supportedFormats: string[];
  maxCount?: number;
}

/**
 * Get the most restrictive image constraints based on selected platforms
 */
export const getImageConstraints = (selectedPlatforms: string[]): ImageConstraints => {
  if (selectedPlatforms.length === 0) {
    // Default constraints when no platforms selected
    return {
      maxSize: 5,
      supportedFormats: ['jpg', 'jpeg', 'png'],
      maxCount: 4
    };
  }

  const platforms = selectedPlatforms
    .map(id => PLATFORMS.find(p => p.id === id))
    .filter((p): p is SocialPlatform => p !== undefined && p.supportsImages);

  if (platforms.length === 0) {
    return {
      maxSize: 0,
      supportedFormats: [],
      maxCount: 0
    };
  }

  // Get the most restrictive constraints
  const maxSize = Math.min(...platforms.map(p => p.maxImageSize));
  const supportedFormats = platforms.reduce((common, platform) => {
    return common.filter(format => platform.supportedImageFormats.includes(format));
  }, platforms[0].supportedImageFormats);

  // Most platforms support 4 images, but some may have different limits
  const maxCount = 4; // Default for most platforms

  return {
    maxSize,
    supportedFormats,
    maxCount
  };
};

/**
 * Get file extension from URI or filename
 */
export const getFileExtension = (uri: string): string => {
  // Handle URLs with query parameters by removing them first
  const urlWithoutQuery = uri.split('?')[0];
  const filename = urlWithoutQuery.split('/').pop() || '';
  const extension = filename.split('.').pop()?.toLowerCase() || '';

  // If no extension found, assume it's a valid image (for URLs like picsum.photos)
  if (!extension && uri.includes('picsum.photos')) {
    return 'jpg'; // Default to jpg for picsum URLs
  }

  return extension;
};

/**
 * Convert bytes to MB
 */
export const bytesToMB = (bytes: number): number => {
  return bytes / (1024 * 1024);
};

/**
 * Validate a single image against platform constraints
 */
export const validateImage = (
  imageUri: string,
  imageSizeBytes: number,
  constraints: ImageConstraints
): ImageValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check file format
  const extension = getFileExtension(imageUri);
  if (!constraints.supportedFormats.includes(extension)) {
    errors.push(
      `Format .${extension} is not supported. Supported formats: ${constraints.supportedFormats.join(', ')}`
    );
  }

  // Check file size
  const sizeInMB = bytesToMB(imageSizeBytes);
  if (sizeInMB > constraints.maxSize) {
    errors.push(
      `Image size (${sizeInMB.toFixed(1)}MB) exceeds limit of ${constraints.maxSize}MB`
    );
  }

  // Add warnings for large files that are still within limits
  if (sizeInMB > constraints.maxSize * 0.8 && sizeInMB <= constraints.maxSize) {
    warnings.push(
      `Large file size (${sizeInMB.toFixed(1)}MB). Consider compressing for faster uploads.`
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate multiple images against platform constraints
 */
export const validateImages = (
  images: Array<{ uri: string; size: number }>,
  constraints: ImageConstraints
): ImageValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Check image count
  if (constraints.maxCount && images.length > constraints.maxCount) {
    allErrors.push(`Too many images (${images.length}). Maximum allowed: ${constraints.maxCount}`);
  }

  // Validate each image
  images.forEach((image, index) => {
    const result = validateImage(image.uri, image.size, constraints);
    
    // Prefix errors and warnings with image number
    result.errors.forEach(error => {
      allErrors.push(`Image ${index + 1}: ${error}`);
    });
    
    result.warnings.forEach(warning => {
      allWarnings.push(`Image ${index + 1}: ${warning}`);
    });
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

/**
 * Get platform names that support images from selected platforms
 */
export const getImageSupportingPlatforms = (selectedPlatforms: string[]): string[] => {
  return selectedPlatforms
    .map(id => PLATFORMS.find(p => p.id === id))
    .filter((p): p is SocialPlatform => p !== undefined && p.supportsImages)
    .map(p => p.name);
};

/**
 * Check if any selected platform supports images
 */
export const hasImageSupport = (selectedPlatforms: string[]): boolean => {
  return selectedPlatforms.some(id => {
    const platform = PLATFORMS.find(p => p.id === id);
    return platform?.supportsImages === true;
  });
};
