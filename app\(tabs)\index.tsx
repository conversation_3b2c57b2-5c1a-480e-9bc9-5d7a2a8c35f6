import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Send, Save, Image as ImageIcon, X, Edit } from 'lucide-react-native';
import PlatformSelector from '@/components/PlatformSelector';
import MessageInput from '@/components/MessageInput';
import ScheduleSelector from '@/components/ScheduleSelector';
import ImagePickerComponent, { SelectedImage } from '@/components/ImagePicker';
import ImagePreview from '@/components/ImagePreview';
import ImageConstraintsInfo from '@/components/ImageConstraintsInfo';
import { Message } from '@/types';
import { storage } from '@/utils/storage';
import { useDraftEditor, convertImagesToSelectedImages } from '@/hooks/useDraftEditor';

export default function ComposeScreen() {
  const [message, setMessage] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [scheduledDate, setScheduledDate] = useState<Date | undefined>();
  const [selectedImages, setSelectedImages] = useState<SelectedImage[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const { editingDraft, isEditing, clearEditingDraft } = useDraftEditor();



  // Load draft data when editing
  useEffect(() => {
    if (isEditing && editingDraft) {
      setMessage(editingDraft.content);
      setSelectedPlatforms(editingDraft.platforms);
      setScheduledDate(editingDraft.scheduledDate);
      setSelectedImages(convertImagesToSelectedImages(editingDraft.images));
    }
  }, [isEditing, editingDraft]);

  const handleSendOrSchedule = async () => {
    // Check if there's content to send (message or images)
    const hasContent = message.trim() || selectedImages.length > 0;

    if (!hasContent) {
      Alert.alert('Error', 'Please enter a message or add images to send');
      return;
    }

    if (selectedPlatforms.length === 0) {
      Alert.alert('Error', 'Please select at least one platform');
      return;
    }

    setIsSaving(true);

    try {
      const messageToSave: Message = isEditing && editingDraft ? {
        ...editingDraft,
        content: message,
        platforms: selectedPlatforms,
        images: selectedImages.length > 0 ? selectedImages.map(img => img.uri) : undefined,
        scheduledDate,
        status: scheduledDate ? 'scheduled' : 'sent',
        sentDate: scheduledDate ? undefined : new Date(),
        updatedAt: new Date(),
      } : {
        id: Date.now().toString(),
        content: message,
        platforms: selectedPlatforms,
        images: selectedImages.length > 0 ? selectedImages.map(img => img.uri) : undefined,
        scheduledDate,
        status: scheduledDate ? 'scheduled' : 'sent',
        sentDate: scheduledDate ? undefined : new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await storage.saveMessage(messageToSave);

      // Reset form and clear editing state
      setMessage('');
      setSelectedPlatforms([]);
      setScheduledDate(undefined);
      setSelectedImages([]);
      if (isEditing) {
        clearEditingDraft();
      }

      Alert.alert(
        'Success',
        isEditing
          ? 'Draft updated and sent successfully!'
          : scheduledDate
          ? 'Message scheduled successfully!'
          : 'Message sent successfully!'
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save message');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveDraft = async () => {
    // Check if there's any content to save
    const hasContent = message.trim() ||
                      selectedPlatforms.length > 0 ||
                      selectedImages.length > 0 ||
                      scheduledDate;

    if (!hasContent) {
      Alert.alert('Error', 'Please add some content before saving as draft (message, platforms, images, or schedule)');
      return;
    }

    setIsSaving(true);

    try {
      const draftMessage: Message = isEditing && editingDraft ? {
        ...editingDraft,
        content: message,
        platforms: selectedPlatforms,
        images: selectedImages.length > 0 ? selectedImages.map(img => img.uri) : undefined,
        status: 'draft',
        updatedAt: new Date(),
      } : {
        id: Date.now().toString(),
        content: message,
        platforms: selectedPlatforms,
        images: selectedImages.length > 0 ? selectedImages.map(img => img.uri) : undefined,
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await storage.saveMessage(draftMessage);

      // Reset form and clear editing state
      setMessage('');
      setSelectedPlatforms([]);
      setScheduledDate(undefined);
      setSelectedImages([]);
      if (isEditing) {
        clearEditingDraft();
      }

      Alert.alert('Success', isEditing ? 'Draft updated successfully!' : 'Draft saved successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save draft');
    } finally {
      setIsSaving(false);
    }
  };

  const handleImagesSelected = (images: SelectedImage[]) => {
    setSelectedImages(images);
  };



  const handleRemoveImage = (index: number) => {
    const updatedImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(updatedImages);
  };

  const handleClearAllImages = () => {
    setSelectedImages([]);
  };

  const handleCancelEditing = () => {
    Alert.alert(
      'Cancel Editing',
      'Are you sure you want to cancel editing this draft? Any unsaved changes will be lost.',
      [
        { text: 'Keep Editing', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: () => {
            // Reset form and clear editing state
            setMessage('');
            setSelectedPlatforms([]);
            setScheduledDate(undefined);
            setSelectedImages([]);
            clearEditingDraft();
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <Text style={styles.title}>
              {isEditing ? 'Edit Draft' : 'Compose Message'}
            </Text>
            <Text style={styles.subtitle}>
              {isEditing ? 'Update your draft message' : 'Share your thoughts across platforms'}
            </Text>
          </View>
          {isEditing && (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelEditing}
            >
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>
        {isEditing && (
          <View style={styles.editingBanner}>
            <Edit size={16} color="#3B82F6" />
            <Text style={styles.editingText}>
              Editing draft from {editingDraft?.createdAt.toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <PlatformSelector
          selectedPlatforms={selectedPlatforms}
          onSelectionChange={setSelectedPlatforms}
        />

        <MessageInput
          value={message}
          onChangeText={setMessage}
          selectedPlatforms={selectedPlatforms}
        />

        <ScheduleSelector
          scheduledDate={scheduledDate}
          onScheduleChange={setScheduledDate}
        />

        <ImageConstraintsInfo
          selectedPlatforms={selectedPlatforms}
          showDetailedInfo={selectedPlatforms.length > 0}
        />

        <ImagePickerComponent
          selectedPlatforms={selectedPlatforms}
          onImagesSelected={handleImagesSelected}
          currentImages={selectedImages}
          disabled={isSaving}
        />

        <ImagePreview
          images={selectedImages}
          selectedPlatforms={selectedPlatforms}
          onRemoveImage={handleRemoveImage}
          onClearAll={handleClearAllImages}
        />

        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.draftButton]}
            onPress={handleSaveDraft}
            disabled={isSaving}
          >
            <Save size={20} color="#6B7280" />
            <Text style={styles.draftButtonText}>
              {isEditing ? 'Update Draft' : 'Save Draft'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.sendButton]}
            onPress={handleSendOrSchedule}
            disabled={isSaving}
          >
            <Send size={20} color="#FFFFFF" />
            <Text style={styles.sendButtonText}>
              {isEditing
                ? (scheduledDate ? 'Update & Schedule' : 'Update & Send')
                : (scheduledDate ? 'Schedule' : 'Send Now')
              }
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  cancelButton: {
    padding: 4,
    marginLeft: 12,
  },
  editingBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  editingText: {
    fontSize: 14,
    color: '#3B82F6',
    marginLeft: 8,
    fontWeight: '500',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  content: {
    flex: 1,
    padding: 20,
  },

  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  draftButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  draftButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
  sendButton: {
    backgroundColor: '#3B82F6',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});