import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Info, Image as ImageIcon, AlertTriangle } from 'lucide-react-native';
import { PLATFORMS } from '@/constants/platforms';
import { getImageConstraints, hasImageSupport, getImageSupportingPlatforms } from '@/utils/imageValidation';

interface ImageConstraintsInfoProps {
  selectedPlatforms: string[];
  showDetailedInfo?: boolean;
}

export default function ImageConstraintsInfo({ 
  selectedPlatforms, 
  showDetailedInfo = false 
}: ImageConstraintsInfoProps) {
  const supportsImages = hasImageSupport(selectedPlatforms);
  const supportingPlatforms = getImageSupportingPlatforms(selectedPlatforms);
  const constraints = getImageConstraints(selectedPlatforms);

  if (selectedPlatforms.length === 0) {
    return (
      <View style={styles.infoContainer}>
        <Info size={16} color="#6B7280" />
        <Text style={styles.infoText}>
          Select platforms to see image requirements
        </Text>
      </View>
    );
  }

  if (!supportsImages) {
    return (
      <View style={styles.warningContainer}>
        <AlertTriangle size={16} color="#F59E0B" />
        <Text style={styles.warningText}>
          None of the selected platforms support images
        </Text>
      </View>
    );
  }

  if (!showDetailedInfo) {
    return (
      <View style={styles.infoContainer}>
        <ImageIcon size={16} color="#6B7280" />
        <Text style={styles.infoText}>
          Max: {constraints.maxSize}MB • Formats: {constraints.supportedFormats.join(', ')}
          {constraints.maxCount && ` • Limit: ${constraints.maxCount} images`}
        </Text>
      </View>
    );
  }

  // Detailed view
  const nonSupportingPlatforms = selectedPlatforms
    .filter(id => !supportingPlatforms.includes(
      PLATFORMS.find(p => p.id === id)?.name || ''
    ))
    .map(id => PLATFORMS.find(p => p.id === id)?.name)
    .filter(Boolean);

  return (
    <View style={styles.detailedContainer}>
      <View style={styles.detailedHeader}>
        <ImageIcon size={18} color="#3B82F6" />
        <Text style={styles.detailedTitle}>Image Requirements</Text>
      </View>

      {supportingPlatforms.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Supported Platforms ({supportingPlatforms.length})
          </Text>
          <Text style={styles.platformList}>
            {supportingPlatforms.join(', ')}
          </Text>
          
          <View style={styles.constraintsList}>
            <View style={styles.constraintItem}>
              <Text style={styles.constraintLabel}>Maximum file size:</Text>
              <Text style={styles.constraintValue}>{constraints.maxSize}MB</Text>
            </View>
            
            <View style={styles.constraintItem}>
              <Text style={styles.constraintLabel}>Supported formats:</Text>
              <Text style={styles.constraintValue}>
                {constraints.supportedFormats.map(format => `.${format}`).join(', ')}
              </Text>
            </View>
            
            {constraints.maxCount && (
              <View style={styles.constraintItem}>
                <Text style={styles.constraintLabel}>Maximum images:</Text>
                <Text style={styles.constraintValue}>{constraints.maxCount} per post</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {nonSupportingPlatforms.length > 0 && (
        <View style={styles.warningSection}>
          <AlertTriangle size={16} color="#F59E0B" />
          <View style={styles.warningContent}>
            <Text style={styles.warningTitle}>
              No Image Support ({nonSupportingPlatforms.length})
            </Text>
            <Text style={styles.warningText}>
              {nonSupportingPlatforms.join(', ')}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.note}>
        <Text style={styles.noteText}>
          💡 Constraints shown are the most restrictive across all selected platforms that support images.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    borderColor: '#E2E8F0',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 13,
    color: '#6B7280',
    marginLeft: 8,
    flex: 1,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFBEB',
    borderColor: '#FED7AA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 13,
    color: '#D97706',
    marginLeft: 8,
    flex: 1,
  },
  detailedContainer: {
    backgroundColor: '#FFFFFF',
    borderColor: '#E5E7EB',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  detailedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  detailedTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 8,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  platformList: {
    fontSize: 13,
    color: '#6B7280',
    marginBottom: 12,
  },
  constraintsList: {
    gap: 8,
  },
  constraintItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  constraintLabel: {
    fontSize: 13,
    color: '#6B7280',
    flex: 1,
  },
  constraintValue: {
    fontSize: 13,
    fontWeight: '500',
    color: '#111827',
  },
  warningSection: {
    flexDirection: 'row',
    backgroundColor: '#FFFBEB',
    borderColor: '#FED7AA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  warningContent: {
    flex: 1,
    marginLeft: 8,
  },
  warningTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#D97706',
    marginBottom: 4,
  },
  note: {
    backgroundColor: '#F0F9FF',
    borderColor: '#BAE6FD',
    borderWidth: 1,
    borderRadius: 6,
    padding: 10,
  },
  noteText: {
    fontSize: 12,
    color: '#0369A1',
    lineHeight: 16,
  },
});
