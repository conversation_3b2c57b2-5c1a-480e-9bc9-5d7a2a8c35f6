import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { X, AlertTriangle, Info } from 'lucide-react-native';
import { SelectedImage } from './ImagePicker';
import { getImageConstraints, validateImages, bytesToMB } from '@/utils/imageValidation';

interface ImagePreviewProps {
  images: SelectedImage[];
  selectedPlatforms: string[];
  onRemoveImage: (index: number) => void;
  onClearAll?: () => void;
}

export default function ImagePreview({
  images,
  selectedPlatforms,
  onRemoveImage,
  onClearAll
}: ImagePreviewProps) {
  if (images.length === 0) {
    return null;
  }

  const constraints = getImageConstraints(selectedPlatforms);
  const validation = validateImages(
    images.map(img => ({ uri: img.uri, size: img.size })),
    constraints
  );

  const handleRemoveImage = (index: number) => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => onRemoveImage(index) }
      ]
    );
  };

  const handleClearAll = () => {
    Alert.alert(
      'Remove All Images',
      'Are you sure you want to remove all images?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove All', style: 'destructive', onPress: onClearAll }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          Selected Images ({images.length})
        </Text>
        {images.length > 1 && onClearAll && (
          <TouchableOpacity onPress={handleClearAll} style={styles.clearAllButton}>
            <Text style={styles.clearAllText}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Validation Messages */}
      {validation.errors.length > 0 && (
        <View style={styles.errorContainer}>
          <AlertTriangle size={16} color="#EF4444" />
          <View style={styles.messageContent}>
            <Text style={styles.errorTitle}>Issues Found:</Text>
            {validation.errors.map((error, index) => (
              <Text key={index} style={styles.errorText}>• {error}</Text>
            ))}
          </View>
        </View>
      )}

      {validation.warnings.length > 0 && (
        <View style={styles.warningContainer}>
          <Info size={16} color="#F59E0B" />
          <View style={styles.messageContent}>
            <Text style={styles.warningTitle}>Warnings:</Text>
            {validation.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>• {warning}</Text>
            ))}
          </View>
        </View>
      )}

      {/* Image Grid */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.imageScroll}
        contentContainerStyle={styles.imageScrollContent}
      >
        {images.map((image, index) => (
          <View key={index} style={styles.imageContainer}>
            <Image source={{ uri: image.uri }} style={styles.image} />
            
            {/* Remove Button */}
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveImage(index)}
            >
              <X size={16} color="#FFFFFF" />
            </TouchableOpacity>

            {/* Image Info */}
            <View style={styles.imageInfo}>
              <Text style={styles.imageInfoText} numberOfLines={1}>
                {image.fileName || `Image ${index + 1}`}
              </Text>
              <Text style={styles.imageSizeText}>
                {bytesToMB(image.size).toFixed(1)}MB
              </Text>
              <Text style={styles.imageDimensionsText}>
                {image.width}×{image.height}
              </Text>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Summary */}
      <View style={styles.summary}>
        <Text style={styles.summaryText}>
          Total: {images.length} image{images.length !== 1 ? 's' : ''} • 
          {' '}{images.reduce((total, img) => total + bytesToMB(img.size), 0).toFixed(1)}MB
        </Text>
        {constraints.maxCount && (
          <Text style={styles.summaryText}>
            Limit: {images.length}/{constraints.maxCount} images
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  clearAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#FEF2F2',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  clearAllText: {
    fontSize: 12,
    color: '#EF4444',
    fontWeight: '500',
  },
  errorContainer: {
    flexDirection: 'row',
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFBEB',
    borderColor: '#FED7AA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  messageContent: {
    flex: 1,
    marginLeft: 8,
  },
  errorTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#EF4444',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 13,
    color: '#DC2626',
    lineHeight: 18,
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#F59E0B',
    marginBottom: 4,
  },
  warningText: {
    fontSize: 13,
    color: '#D97706',
    lineHeight: 18,
  },
  imageScroll: {
    marginBottom: 12,
  },
  imageScrollContent: {
    paddingRight: 16,
  },
  imageContainer: {
    marginRight: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
    width: 120,
  },
  image: {
    width: 118,
    height: 118,
    backgroundColor: '#F3F4F6',
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageInfo: {
    padding: 8,
  },
  imageInfoText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  imageSizeText: {
    fontSize: 11,
    color: '#6B7280',
  },
  imageDimensionsText: {
    fontSize: 11,
    color: '#9CA3AF',
  },
  summary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  summaryText: {
    fontSize: 12,
    color: '#6B7280',
  },
});
