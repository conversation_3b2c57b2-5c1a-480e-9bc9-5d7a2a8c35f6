import { Message, AppSettings } from '@/types';

const MESSAGES_KEY = 'social_media_messages';
const SETTINGS_KEY = 'app_settings';

// Mock storage using in-memory store for demo
// In production, use AsyncStorage or similar
let messagesStore: Message[] = [];
let settingsStore: AppSettings = {
  defaultPlatforms: ['twitter', 'facebook'],
  enableNotifications: true,
  autoSave: true,
  theme: 'auto',
  timezone: 'UTC'
};

export const storage = {
  // Messages
  async getMessages(): Promise<Message[]> {
    return messagesStore;
  },

  async saveMessage(message: Message): Promise<void> {
    const existingIndex = messagesStore.findIndex(m => m.id === message.id);
    if (existingIndex >= 0) {
      messagesStore[existingIndex] = message;
    } else {
      messagesStore.push(message);
    }
  },

  async deleteMessage(id: string): Promise<void> {
    messagesStore = messagesStore.filter(m => m.id !== id);
  },

  // Settings
  async getSettings(): Promise<AppSettings> {
    return settingsStore;
  },

  async saveSettings(settings: AppSettings): Promise<void> {
    settingsStore = settings;
  }
};

// Generate mock data for demo
export const generateMockMessages = (): Message[] => {
  const mockMessages: Message[] = [
    {
      id: '1',
      content: 'Excited to announce our new product launch! 🚀 #innovation #tech',
      platforms: ['twitter', 'linkedin'],
      scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      status: 'scheduled',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '2',
      content: 'Just finished an amazing team meeting. Great ideas flowing! 💡',
      platforms: ['facebook', 'twitter'],
      sentDate: new Date(Date.now() - 6 * 60 * 60 * 1000),
      status: 'sent',
      createdAt: new Date(Date.now() - 7 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000)
    },
    {
      id: '3',
      content: 'Working on something special. Can\'t wait to share more details soon! 🎯',
      platforms: ['instagram', 'facebook'],
      images: ['https://picsum.photos/400/400?random=1', 'https://picsum.photos/400/400?random=2'],
      status: 'draft',
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: '4',
      content: 'Beautiful sunset from our office! 🌅 #worklife #nature',
      platforms: ['instagram', 'twitter'],
      images: ['https://picsum.photos/400/400?random=3'],
      sentDate: new Date(Date.now() - 12 * 60 * 60 * 1000),
      status: 'sent',
      createdAt: new Date(Date.now() - 13 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000)
    }
  ];

  messagesStore = mockMessages;
  return mockMessages;
};