import { Message, AppSettings } from '@/types';

const MESSAGES_KEY = 'social_media_messages';
const SETTINGS_KEY = 'app_settings';

// Mock storage using in-memory store for demo
// In production, use AsyncStorage or similar
let messagesStore: Message[] = [];
let settingsStore: AppSettings = {
  defaultPlatforms: ['twitter', 'facebook'],
  enableNotifications: true,
  autoSave: true,
  theme: 'auto',
  timezone: 'UTC'
};

export const storage = {
  // Messages
  async getMessages(): Promise<Message[]> {
    return messagesStore;
  },

  async saveMessage(message: Message): Promise<void> {
    const existingIndex = messagesStore.findIndex(m => m.id === message.id);
    if (existingIndex >= 0) {
      messagesStore[existingIndex] = message;
    } else {
      messagesStore.push(message);
    }
  },

  async deleteMessage(id: string): Promise<void> {
    messagesStore = messagesStore.filter(m => m.id !== id);
  },

  // Settings
  async getSettings(): Promise<AppSettings> {
    return settingsStore;
  },

  async saveSettings(settings: AppSettings): Promise<void> {
    settingsStore = settings;
  }
};

