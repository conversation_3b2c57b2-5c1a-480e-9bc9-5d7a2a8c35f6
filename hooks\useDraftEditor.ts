import { useState, useCallback } from 'react';
import { Message } from '@/types';
import { SelectedImage } from '@/components/ImagePicker';

interface DraftEditorState {
  editingDraft: Message | null;
  isEditing: boolean;
}

interface DraftEditorActions {
  startEditingDraft: (draft: Message) => void;
  clearEditingDraft: () => void;
  updateDraft: (updatedDraft: Message) => void;
}

// Global state for draft editing (simple implementation)
let globalDraftState: DraftEditorState = {
  editingDraft: null,
  isEditing: false,
};

const listeners: Set<() => void> = new Set();

const notifyListeners = () => {
  listeners.forEach(listener => listener());
};

export const useDraftEditor = (): DraftEditorState & DraftEditorActions => {
  const [, forceUpdate] = useState({});

  // Subscribe to global state changes
  const rerender = useCallback(() => {
    forceUpdate({});
  }, []);

  // Add listener on mount, remove on unmount
  useState(() => {
    listeners.add(rerender);
    return () => {
      listeners.delete(rerender);
    };
  });

  const startEditingDraft = useCallback((draft: Message) => {
    globalDraftState = {
      editingDraft: draft,
      isEditing: true,
    };
    notifyListeners();
  }, []);

  const clearEditingDraft = useCallback(() => {
    globalDraftState = {
      editingDraft: null,
      isEditing: false,
    };
    notifyListeners();
  }, []);

  const updateDraft = useCallback((updatedDraft: Message) => {
    globalDraftState = {
      editingDraft: updatedDraft,
      isEditing: true,
    };
    notifyListeners();
  }, []);

  return {
    ...globalDraftState,
    startEditingDraft,
    clearEditingDraft,
    updateDraft,
  };
};

// Helper function to convert image URIs to SelectedImage objects
export const convertImagesToSelectedImages = (imageUris?: string[]): SelectedImage[] => {
  if (!imageUris || imageUris.length === 0) {
    return [];
  }

  return imageUris.map((uri, index) => ({
    uri,
    width: 400, // Default dimensions since we don't have the original data
    height: 400,
    size: 1024 * 1024, // Default 1MB size estimate
    type: 'image',
    fileName: `image_${index + 1}.jpg`,
  }));
};
