{"expo": {"name": "bolt-expo-nativewind", "slug": "bolt-expo-nativewind", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take photos for social media posts.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images for social media posts."}}, "android": {"permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them in your social media posts.", "cameraPermission": "The app accesses your camera to let you take photos for your social media posts."}]], "experiments": {"typedRoutes": true}}}