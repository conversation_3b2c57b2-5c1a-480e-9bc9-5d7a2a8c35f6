import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Filter, Search } from 'lucide-react-native';
import MessageCard from '@/components/MessageCard';
import { Message } from '@/types';
import { storage, generateMockMessages } from '@/utils/storage';
import { useDraftEditor } from '@/hooks/useDraftEditor';

export default function HistoryScreen() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<Message[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'sent' | 'scheduled' | 'draft'>('all');

  const { startEditingDraftAndNavigate } = useDraftEditor();

  useEffect(() => {
    loadMessages();
  }, []);

  // Refresh messages when screen is focused (in a real app, you'd use navigation focus events)
  useEffect(() => {
    const interval = setInterval(() => {
      loadMessages();
    }, 2000); // Refresh every 2 seconds to catch updates

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    filterMessages();
  }, [messages, selectedFilter]);

  const loadMessages = async () => {
    try {
      let messagesData = await storage.getMessages();
      
      // Generate mock data if no messages exist
      if (messagesData.length === 0) {
        messagesData = generateMockMessages();
      }

      // Sort by created date, newest first
      messagesData.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      setMessages(messagesData);
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  const filterMessages = () => {
    if (selectedFilter === 'all') {
      setFilteredMessages(messages);
    } else {
      setFilteredMessages(messages.filter(message => message.status === selectedFilter));
    }
  };

  const handleMessagePress = (message: Message) => {
    if (message.status === 'draft') {
      // Edit draft - directly navigate to compose screen
      startEditingDraftAndNavigate(message);
    } else {
      // Show message details for sent/scheduled messages
      Alert.alert(
        'Message Details',
        `Status: ${message.status}\nPlatforms: ${message.platforms.join(', ')}\nContent: ${message.content}`,
        [{ text: 'OK' }]
      );
    }
  };

  const getFilterButtonStyle = (filter: typeof selectedFilter) => [
    styles.filterButton,
    selectedFilter === filter && styles.activeFilterButton
  ];

  const getFilterTextStyle = (filter: typeof selectedFilter) => [
    styles.filterText,
    selectedFilter === filter && styles.activeFilterText
  ];

  const renderMessage = ({ item }: { item: Message }) => (
    <MessageCard message={item} onPress={() => handleMessagePress(item)} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Message History</Text>
        <Text style={styles.subtitle}>Track your posts and campaigns</Text>
      </View>

      <View style={styles.filters}>
        <TouchableOpacity
          style={getFilterButtonStyle('all')}
          onPress={() => setSelectedFilter('all')}
        >
          <Text style={getFilterTextStyle('all')}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={getFilterButtonStyle('sent')}
          onPress={() => setSelectedFilter('sent')}
        >
          <Text style={getFilterTextStyle('sent')}>Sent</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={getFilterButtonStyle('scheduled')}
          onPress={() => setSelectedFilter('scheduled')}
        >
          <Text style={getFilterTextStyle('scheduled')}>Scheduled</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={getFilterButtonStyle('draft')}
          onPress={() => setSelectedFilter('draft')}
        >
          <Text style={getFilterTextStyle('draft')}>Drafts</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredMessages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.messagesList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No messages found</Text>
            <Text style={styles.emptySubtext}>
              Start composing to see your messages here
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  filters: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterButton: {
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  messagesList: {
    padding: 20,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
});